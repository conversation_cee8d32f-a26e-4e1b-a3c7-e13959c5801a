using HelixToolkit.Wpf.SharpDX;
using Microsoft.Win32;
using OxyPlot;
using OxyPlot.Axes;
using OxyPlot.Series;
using SharpDX;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Media3D;

namespace WPF_PCV.Views.Pages
{
    /// <summary>
    /// 点云参数分析页面 - 支持流式渲染和2D切片分析
    /// </summary>
    public partial class Parameters : Page
    {
        #region === 核心数据模型 ===

        // 点云数据存储 - 统一使用Vector3提升性能
        private readonly List<Vector3> pointCloudData = new();
        private List<Vector3> fullPoints = new();
        private List<Vector3> renderPoints = new();

        // 2D切片数据 - 支持多轴向
        private List<List<System.Windows.Point>> slices2D = new();
        private List<double> sliceLevels = new(); // 通用层级列表（可能是Z或Y）
        private int currentSlice = 0;
        private double threshold;

        // 轴向检测
        private SliceAxis detectedAxis = SliceAxis.Z; // 默认Z轴
        private string axisLabel = "Z"; // 轴标签显示

        // 3D视图选择状态
        private int selectedLayerIndex = -1;
        private PointGeometryModel3D? selectedLayerVisual;

        // 全局变换参数（解决流式渲染数据失真）
        private Vector3 globalCenter;
        private double globalScale = 1.0;
        private bool globalTransformCalculated = false;

        // 降采样控制
        private int downsampleLevel = 1; // 1-10，数值越大降采样越狠

        #endregion

        #region === 常量定义 ===

        /// <summary>
        /// 性能和配置常量
        /// </summary>
        private static class Constants
        {
            // 流式渲染常量
            public const int FIRST_RENDER_CHUNK_SIZE = 10000;
            public const int STREAMING_CHUNK_SIZE = 5000;

            // 切片处理常量
            public const double DEFAULT_TOLERANCE = 1e-5;
            public const int MAX_CACHED_SLICES = 10;

            // 轴向检测常量
            public const int AXIS_DETECTION_SAMPLE_SIZE = 1000;
            public const double AXIS_DETECTION_THRESHOLD = 1.5;

            // UI更新常量
            public const int UI_UPDATE_BATCH_SIZE = 100;
            public const double MARKER_SIZE_BASE = 2.0;
            public const double MARKER_SIZE_MIN = 1.0;
            public const double MARKER_SIZE_MAX = 5.0;
        }

        #endregion

        #region === 轴向检测枚举 ===

        /// <summary>
        /// 切片轴向枚举
        /// </summary>
        public enum SliceAxis
        {
            Z, // Z轴移动，按Z分层（xOy平面切片）
            Y  // Y轴移动，按Y分层（xOZ平面切片）
        }

        #endregion

        #region === 渲染配置 ===

        private const int MaxRenderPoints = 70_000_000;
        private const int FIRST_RENDER_CHUNK_SIZE = 100_000;
        private const int STREAMING_CHUNK_SIZE = 500_000;

        // 流式渲染状态
        private bool isStreamingMode = false;
        private CancellationTokenSource? streamingCancellation;
        private long totalPointsRendered = 0;

        #endregion

        #region === 组件实例 ===

        private readonly ParametersPointCloudFileLoader fileLoader;
        private readonly ParametersPointCloudVisualizer visualizer;
        private PlotModel slicePlotModel;

        #endregion

        #region === 初始化 ===

        public Parameters()
        {
            InitializeComponent();

            // 初始化组件
            fileLoader = new ParametersPointCloudFileLoader();
            visualizer = new ParametersPointCloudVisualizer(helixViewport);

            // 初始化3D视口
            InitializeViewport();

            // 初始化2D绘图
            InitializePlotView();

            // 绑定事件
            BindEvents();
        }

        private void InitializeViewport()
        {
            helixViewport.Camera = new HelixToolkit.Wpf.SharpDX.PerspectiveCamera
            {
                Position = new Point3D(10, 10, 10),
                LookDirection = new Vector3D(-10, -10, -10),
                UpDirection = new Vector3D(0, 0, 1),
                NearPlaneDistance = 0.1,
                FarPlaneDistance = 100000
            };
        }

        private void InitializePlotView()
        {
            slicePlotModel = new PlotModel { Background = OxyColors.WhiteSmoke };
            slicePlotModel.Axes.Add(new LinearAxis { Position = AxisPosition.Bottom, Title = "X" });
            slicePlotModel.Axes.Add(new LinearAxis { Position = AxisPosition.Left, Title = "Y/Z" }); // 动态轴标签
            PlotView.Model = slicePlotModel;
        }

        private void BindEvents()
        {
            fileLoader.LoadCompleted += OnLoadCompleted;
            SectionListBox.MouseDoubleClick += SectionListBox_MouseDoubleClick;
            PlotView.SizeChanged += (s, e) =>
            {
                if (sliceLevels.Count > 0)
                {
                    EnforceAspectRatio();
                    PlotView.InvalidatePlot(false);
                }
            };
        }

        #endregion

        #region === 文件加载处理 ===

        /// <summary>
        /// 文件打开按钮点击
        /// </summary>
        private void BtnOpenFile_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "选择点云文件",
                Filter = "Point Cloud files (*.txt;*.csv;*.dat)|*.txt;*.csv;*.dat|Text files (*.txt)|*.txt|CSV files (*.csv)|*.csv|All files (*.*)|*.*",
                InitialDirectory = Environment.CurrentDirectory
            };

            if (openFileDialog.ShowDialog() == true)
            {
                txtFilePath.Text = openFileDialog.FileName;
                LoadPointCloudFileAsync(openFileDialog.FileName);
            }
        }

        /// <summary>
        /// 异步加载点云文件
        /// </summary>
        private async void LoadPointCloudFileAsync(string filePath)
        {
            btnOpenFile.IsEnabled = false;
            txtStatus.Text = "加载中...";
            this.Cursor = Cursors.Wait;

            try
            {
                var result = await fileLoader.LoadPointCloudAsync(filePath);
                if (result.IsSuccess)
                {
                    pointCloudData.Clear();
                    pointCloudData.AddRange(result.Points);
                }
            }
            catch (Exception ex)
            {
                HandleLoadError($"意外错误: {ex.Message}");
            }
            finally
            {
                this.Cursor = Cursors.Arrow;
            }
        }

        /// <summary>
        /// 文件加载完成事件处理
        /// </summary>
        private void OnLoadCompleted(object? sender, PointCloudLoadResult result)
        {
            Dispatcher.Invoke(() =>
            {
                if (result.IsSuccess)
                {
                    HandleSuccessfulLoad(result);
                }
                else
                {
                    HandleLoadError(result.ErrorMessage);
                }
                btnOpenFile.IsEnabled = true;
            });
        }

        /// <summary>
        /// 处理成功加载的结果
        /// </summary>
        private async void HandleSuccessfulLoad(PointCloudLoadResult result)
        {
            ResetRenderingState();
            pointCloudData.Clear();
            pointCloudData.AddRange(result.Points);

            // 选择渲染策略
            if (result.Points.Count > FIRST_RENDER_CHUNK_SIZE)
            {
                await HandleStreamingRender(result);
            }
            else
            {
                await HandleStandardRender(result);
            }
        }

        /// <summary>
        /// 重置渲染状态
        /// </summary>
        private void ResetRenderingState()
        {
            selectedLayerIndex = -1;
            if (selectedLayerVisual != null)
            {
                helixViewport.Items.Remove(selectedLayerVisual);
                selectedLayerVisual = null;
            }

            globalTransformCalculated = false;
            globalCenter = new Vector3(0, 0, 0);
            globalScale = 1.0;
        }

        /// <summary>
        /// 处理加载错误
        /// </summary>
        private void HandleLoadError(string errorMessage)
        {
            MessageBox.Show($"加载文件错误: {errorMessage}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }

        #endregion

        #region === 流式渲染处理 ===

        /// <summary>
        /// 流式渲染处理
        /// </summary>
        private async Task HandleStreamingRender(PointCloudLoadResult result)
        {
            isStreamingMode = true;
            streamingCancellation = new CancellationTokenSource();
            var token = streamingCancellation.Token;

            try
            {
                // 1. 计算全局变换参数
                await CalculateGlobalTransformAsync(result.Points, token);

                // 2. 首帧快速渲染
                RenderFirstChunk(result.Points, token);

                // 3. 流式渲染剩余数据
                await StreamRemainingData(result.Points, token);

                // 4. 优化3D显示和构建2D切片
                await FinalizeRendering(result, token);
            }
            catch (OperationCanceledException)
            {
                txtStatus.Text = "加载已取消";
            }
            finally
            {
                CleanupStreamingResources();
            }
        }

        /// <summary>
        /// 计算全局变换参数
        /// </summary>
        private async Task CalculateGlobalTransformAsync(List<Vector3> points, CancellationToken token)
        {
            await Task.Run(() =>
            {
                CalculateGlobalTransform(points);
            }, token);
        }

        /// <summary>
        /// 首帧快速渲染 - 优化为Vector3
        /// </summary>
        private void RenderFirstChunk(List<Vector3> points, CancellationToken token)
        {
            var firstChunk = points.Take(FIRST_RENDER_CHUNK_SIZE).ToList();

            ApplyGlobalTransform(firstChunk);
            visualizer.CreatePointCloudVisualizationVector3(firstChunk);
            totalPointsRendered = firstChunk.Count;

            txtStatus.Text = $"首帧渲染: {totalPointsRendered:N0} 点 • 后台加载中...";
        }

        /// <summary>
        /// 流式渲染剩余数据
        /// </summary>
        private async Task StreamRemainingData(List<Vector3> points, CancellationToken token)
        {
            await Task.Run(async () =>
            {
                var remainingPoints = points.Skip(FIRST_RENDER_CHUNK_SIZE).ToList();

                for (int i = 0; i < remainingPoints.Count; i += STREAMING_CHUNK_SIZE)
                {
                    if (token.IsCancellationRequested) break;

                    var chunk = remainingPoints.Skip(i).Take(STREAMING_CHUNK_SIZE).ToList();

                    ApplyGlobalTransform(chunk);

                    await Dispatcher.InvokeAsync(() =>
                    {
                        visualizer.AddPointsToVisualizationVector3(chunk);
                        totalPointsRendered += chunk.Count;

                        var progress = (double)totalPointsRendered / points.Count * 100;
                        txtStatus.Text = $"流式渲染: {totalPointsRendered:N0}/{points.Count:N0} 点 ({progress:F1}%)";
                    });

                    await Task.Delay(10, token);
                }
            }, token);
        }

        /// <summary>
        /// 完成渲染处理
        /// </summary>
        private async Task FinalizeRendering(PointCloudLoadResult result, CancellationToken token)
        {
            // 准备完整数据
            await Task.Run(() =>
            {
                fullPoints = new List<Vector3>(pointCloudData);
                ApplyGlobalTransform(fullPoints);
            }, token);

            // 优化3D显示
            await OptimizeRenderingFor3D(result, token);

            // 异步构建2D切片
            BuildSlicesAsync(result);
        }

        /// <summary>
        /// 优化3D渲染显示 - 使用Vector3和降采样级别
        /// </summary>
        private async Task OptimizeRenderingFor3D(PointCloudLoadResult result, CancellationToken token)
        {
            await Task.Run(() =>
            {
                // 应用降采样
                var downsampledFor3D = VoxelGridDownsample(fullPoints, downsampleLevel);

                Dispatcher.Invoke(() =>
                {
                    visualizer.CreatePointCloudVisualizationVector3(downsampledFor3D);
                    renderPoints = downsampledFor3D;
                    visualizer.AdjustCameraToFitPointCloudPublicVector3(fullPoints);
                });
            }, token);
        }

        /// <summary>
        /// 异步构建2D切片 - 优化为Vector3
        /// </summary>
        private void BuildSlicesAsync(PointCloudLoadResult result)
        {
            _ = Task.Run(async () =>
            {
                try
                {
                    var sliceData = BuildSliceDataInBackground(fullPoints);

                    await Dispatcher.InvokeAsync(() =>
                    {
                        ApplySliceDataToUI(sliceData);
                        var reductionRatio = fullPoints.Count > 0 ? (double)renderPoints.Count / fullPoints.Count : 1.0;
                        txtStatus.Text = $"完成: {renderPoints.Count:N0}/{fullPoints.Count:N0} ({reductionRatio:P1}) • {result.LoadTimeMs}ms | 降采样级别: {downsampleLevel}";
                    });
                }
                catch (Exception ex)
                {
                    await Dispatcher.InvokeAsync(() =>
                    {
                        txtStatus.Text = $"2D切片构建失败: {ex.Message}";
                    });
                }
            });
        }

        /// <summary>
        /// 清理流式渲染资源
        /// </summary>
        private void CleanupStreamingResources()
        {
            isStreamingMode = false;
            streamingCancellation?.Dispose();
            streamingCancellation = null;
        }

        #endregion

        #region === 标准渲染处理 ===

        /// <summary>
        /// 标准渲染处理（小数据集）- 优化为Vector3
        /// </summary>
        private async Task HandleStandardRender(PointCloudLoadResult result)
        {
            // 数据准备
            await Task.Run(() =>
            {
                fullPoints = new List<Vector3>(pointCloudData);
                CalculateGlobalTransform(fullPoints);
                ApplyGlobalTransform(fullPoints);

                // 应用降采样
                renderPoints = VoxelGridDownsample(fullPoints, downsampleLevel);
            });

            // 3D渲染
            visualizer.CreatePointCloudVisualizationVector3(renderPoints);

            // 异步构建2D切片
            BuildSlicesAsync(result);

            // 更新状态
            var reductionRatio = fullPoints.Count > 0 ? (double)renderPoints.Count / fullPoints.Count : 1.0;
            txtStatus.Text = $"Points: {renderPoints.Count:N0}/{fullPoints.Count:N0} ({reductionRatio:P1}) • {result.LoadTimeMs}ms | 降采样级别: {downsampleLevel}";
        }

        #endregion

        #region === 数据变换处理 ===

        /// <summary>
        /// 计算全局变换参数（解决流式渲染数据失真）- 优化为Vector3
        /// </summary>
        private void CalculateGlobalTransform(List<Vector3> allPoints)
        {
            if (allPoints.Count == 0)
            {
                globalCenter = new Vector3(0, 0, 0);
                globalScale = 1.0;
                globalTransformCalculated = true;
                return;
            }

            // 计算边界框
            var minX = allPoints.Min(p => p.X);
            var maxX = allPoints.Max(p => p.X);
            var minY = allPoints.Min(p => p.Y);
            var maxY = allPoints.Max(p => p.Y);
            var minZ = allPoints.Min(p => p.Z);
            var maxZ = allPoints.Max(p => p.Z);

            // 计算中心点和缩放因子
            globalCenter = new Vector3((minX + maxX) / 2, (minY + maxY) / 2, (minZ + maxZ) / 2);
            var maxRange = Math.Max(Math.Max(maxX - minX, maxY - minY), maxZ - minZ);
            globalScale = maxRange > 0 ? 100.0 / maxRange : 1.0;
            globalTransformCalculated = true;
        }

        /// <summary>
        /// 应用全局变换到点云数据 - 优化为Vector3
        /// </summary>
        private void ApplyGlobalTransform(List<Vector3> points)
        {
            if (!globalTransformCalculated || points.Count == 0) return;

            for (int i = 0; i < points.Count; i++)
            {
                var p = points[i];
                points[i] = new Vector3(
                    (p.X - globalCenter.X) * (float)globalScale,
                    (p.Y - globalCenter.Y) * (float)globalScale,
                    (p.Z - globalCenter.Z) * (float)globalScale
                );
            }
        }

        #endregion

        #region === 2D切片处理 ===

        /// <summary>
        /// 在后台构建切片数据（避免阻塞UI）- 支持多轴向优化
        /// </summary>
        private SliceData BuildSliceDataInBackground(List<Vector3> pts3D)
        {
            if (pts3D.Count == 0)
                return new SliceData();

            // 1. 自动检测轴向
            detectedAxis = DetectSliceAxis(pts3D);
            axisLabel = detectedAxis == SliceAxis.Z ? "Z" : "Y";

            // 2. 根据检测到的轴向构建切片
            const double tolerance = 1e-5;
            var uniqueLevels = ExtractUniqueLevels(pts3D, detectedAxis, tolerance);
            var slices2D = BuildSlicesFromLevels(pts3D, uniqueLevels, detectedAxis, tolerance);

            return new SliceData
            {
                SliceLevels = uniqueLevels,
                Slices2D = slices2D,
                Threshold = tolerance,
                Axis = detectedAxis,
                AxisLabel = axisLabel
            };
        }

        /// <summary>
        /// 自动检测点云数据的主要移动轴向
        /// </summary>
        private SliceAxis DetectSliceAxis(List<Vector3> points)
        {
            if (points.Count < 100) return SliceAxis.Z; // 数据太少，默认Z轴

            // 采样前1000个点进行检测（提高性能）
            var sampleSize = Math.Min(1000, points.Count);
            var samplePoints = points.Take(sampleSize).ToList();

            // 统计连续相同值的情况
            int zConsecutiveCount = CountConsecutiveSameValues(samplePoints, p => p.Z);
            int yConsecutiveCount = CountConsecutiveSameValues(samplePoints, p => p.Y);

            // 判断哪个轴有更明显的分层特征
            if (yConsecutiveCount > zConsecutiveCount * 1.5) // Y轴分层更明显
            {
                return SliceAxis.Y;
            }
            else
            {
                return SliceAxis.Z; // 默认或Z轴分层更明显
            }
        }

        /// <summary>
        /// 统计连续相同值的数量
        /// </summary>
        private int CountConsecutiveSameValues(List<Vector3> points, Func<Vector3, float> selector)
        {
            if (points.Count < 2) return 0;

            int consecutiveCount = 0;
            int maxConsecutive = 0;
            float lastValue = selector(points[0]);

            for (int i = 1; i < points.Count; i++)
            {
                float currentValue = selector(points[i]);
                if (Math.Abs(currentValue - lastValue) < 1e-5f) // 相同值
                {
                    consecutiveCount++;
                    maxConsecutive = Math.Max(maxConsecutive, consecutiveCount);
                }
                else
                {
                    consecutiveCount = 0;
                }
                lastValue = currentValue;
            }

            return maxConsecutive;
        }

        /// <summary>
        /// 提取唯一层级 - 支持多轴向优化
        /// </summary>
        private List<double> ExtractUniqueLevels(List<Vector3> points, SliceAxis axis, double tolerance)
        {
            // 根据轴向选择对应的坐标值
            var values = axis == SliceAxis.Z
                ? points.Select(p => (double)p.Z).OrderBy(v => v).ToList()
                : points.Select(p => (double)p.Y).OrderBy(v => v).ToList();

            var uniqueLevels = new List<double>();

            foreach (var value in values)
            {
                if (uniqueLevels.Count == 0 || Math.Abs(value - uniqueLevels.Last()) > tolerance)
                    uniqueLevels.Add(value);
            }

            return uniqueLevels;
        }

        /// <summary>
        /// 根据层级构建2D切片 - 支持多轴向优化
        /// </summary>
        private List<List<System.Windows.Point>> BuildSlicesFromLevels(List<Vector3> points, List<double> levels, SliceAxis axis, double tolerance)
        {
            var slices = new List<List<System.Windows.Point>>();

            foreach (var level in levels)
            {
                List<System.Windows.Point> slice;

                if (axis == SliceAxis.Z)
                {
                    // Z轴分层：xOy平面切片
                    slice = points.Where(p => Math.Abs(p.Z - level) <= tolerance)
                                 .Select(p => new System.Windows.Point(p.X, p.Y))
                                 .ToList();
                }
                else // SliceAxis.Y
                {
                    // Y轴分层：xOZ平面切片
                    slice = points.Where(p => Math.Abs(p.Y - level) <= tolerance)
                                 .Select(p => new System.Windows.Point(p.X, p.Z))
                                 .ToList();
                }

                slices.Add(slice);
            }

            return slices;
        }

        /// <summary>
        /// 将切片数据应用到UI - 支持多轴向
        /// </summary>
        private void ApplySliceDataToUI(SliceData sliceData)
        {
            sliceLevels = sliceData.SliceLevels;
            slices2D = sliceData.Slices2D;
            threshold = sliceData.Threshold;
            detectedAxis = sliceData.Axis;
            axisLabel = sliceData.AxisLabel;

            currentSlice = sliceLevels.Count > 0 ? sliceLevels.Count / 2 : 0;
            UpdateSliceView();
            PopulateSectionListBox();

            txtStatus.Text += $" | 检测到 {sliceLevels.Count} 层 ({axisLabel}轴分层)";
        }

        /// <summary>
        /// 切片数据结构 - 支持多轴向
        /// </summary>
        private class SliceData
        {
            public List<double> SliceLevels { get; set; } = new();
            public List<List<System.Windows.Point>> Slices2D { get; set; } = new();
            public double Threshold { get; set; } = 1e-5;
            public SliceAxis Axis { get; set; } = SliceAxis.Z;
            public string AxisLabel { get; set; } = "Z";
        }

        #endregion

        #region === 3D视图交互 ===

        /// <summary>
        /// 切片列表双击事件处理 - 支持多轴向
        /// </summary>
        private void SectionListBox_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (SectionListBox.SelectedItem != null)
            {
                int selectedIndex = SectionListBox.SelectedIndex;
                if (selectedIndex >= 0 && selectedIndex < sliceLevels.Count)
                {
                    selectedLayerIndex = selectedIndex;
                    Update3DViewWithSelectedLayer();

                    currentSlice = selectedIndex;
                    UpdateSliceView();
                }
            }
        }

        /// <summary>
        /// 更新3D视图中的选中层颜色 - 支持多轴向
        /// </summary>
        private void Update3DViewWithSelectedLayer()
        {
            if (sliceLevels.Count == 0 || selectedLayerIndex < 0 || selectedLayerIndex >= sliceLevels.Count)
                return;

            // 清除之前的选中层显示
            ClearSelectedLayerVisual();

            // 获取选中层的值和源点
            double selectedLevel = sliceLevels[selectedLayerIndex];
            List<Vector3> sourcePoints = fullPoints.Count > 0 ? fullPoints : renderPoints;

            if (sourcePoints.Count == 0) return;

            // 根据轴向筛选该层的点
            List<Vector3> layerPoints;
            if (detectedAxis == SliceAxis.Z)
            {
                layerPoints = sourcePoints.Where(p => Math.Abs(p.Z - selectedLevel) <= threshold).ToList();
            }
            else // SliceAxis.Y
            {
                layerPoints = sourcePoints.Where(p => Math.Abs(p.Y - selectedLevel) <= threshold).ToList();
            }

            if (layerPoints.Count > 0)
            {
                CreateSelectedLayerVisual(layerPoints);
            }
        }

        /// <summary>
        /// 清除选中层可视化
        /// </summary>
        private void ClearSelectedLayerVisual()
        {
            if (selectedLayerVisual != null)
            {
                helixViewport.Items.Remove(selectedLayerVisual);
                selectedLayerVisual = null;
            }
        }

        /// <summary>
        /// 创建选中层可视化
        /// </summary>
        private void CreateSelectedLayerVisual(List<Vector3> layerPoints)
        {
            var pointGeometry = new PointGeometry3D();
            pointGeometry.Positions = new Vector3Collection(layerPoints);

            selectedLayerVisual = new PointGeometryModel3D
            {
                Geometry = pointGeometry,
                Color = System.Windows.Media.Colors.Red,
                Size = new Size(4.0, 4.0)
            };

            helixViewport.Items.Add(selectedLayerVisual);
        }

        #endregion

        #region === 数据处理算法 ===

        /// <summary>
        /// 体素网格降采样算法 - 优化为Vector3，支持降采样级别控制
        /// </summary>
        public List<Vector3> VoxelGridDownsample(List<Vector3> points, int downsampleLevel = 1)
        {
            if (points?.Count <= 2 || downsampleLevel < 1)
                return points ?? new List<Vector3>();

            // 根据降采样级别计算目标点数和体素大小
            int targetPoints = CalculateTargetPoints(points.Count, downsampleLevel);
            if (points.Count <= targetPoints)
                return new List<Vector3>(points);

            var bounds = CalculateBoundsVector3(points);
            double voxelSize = CalculateOptimalVoxelSizeVector3(bounds, targetPoints, downsampleLevel);
            var voxelGrid = BuildVoxelGridVector3(points, bounds, voxelSize);

            return ExtractRepresentativePointsVector3(voxelGrid);
        }

        /// <summary>
        /// 根据降采样级别计算目标点数
        /// </summary>
        private int CalculateTargetPoints(int originalCount, int downsampleLevel)
        {
            // 降采样级别1-10，级别越高保留的点越少
            double retentionRatio = Math.Max(0.1, 1.0 - (downsampleLevel - 1) * 0.1);
            return Math.Max(1000, (int)(originalCount * retentionRatio));
        }

        /// <summary>
        /// 计算Vector3点云边界
        /// </summary>
        private (float minX, float maxX, float minY, float maxY, float minZ, float maxZ) CalculateBoundsVector3(List<Vector3> points)
        {
            return (
                points.Min(p => p.X), points.Max(p => p.X),
                points.Min(p => p.Y), points.Max(p => p.Y),
                points.Min(p => p.Z), points.Max(p => p.Z)
            );
        }

        /// <summary>
        /// 计算最优体素大小 - Vector3版本，考虑降采样级别
        /// </summary>
        private double CalculateOptimalVoxelSizeVector3((float minX, float maxX, float minY, float maxY, float minZ, float maxZ) bounds, int targetPoints, int downsampleLevel)
        {
            var volume = (bounds.maxX - bounds.minX) * (bounds.maxY - bounds.minY) * (bounds.maxZ - bounds.minZ);
            var baseVoxelSize = Math.Pow(volume / targetPoints, 1.0 / 3.0);

            // 根据降采样级别调整体素大小，级别越高体素越大
            var levelMultiplier = 1.0 + (downsampleLevel - 1) * 0.2;
            return baseVoxelSize * levelMultiplier;
        }

        /// <summary>
        /// 构建Vector3体素网格
        /// </summary>
        private Dictionary<(int, int, int), List<Vector3>> BuildVoxelGridVector3(List<Vector3> points,
            (float minX, float maxX, float minY, float maxY, float minZ, float maxZ) bounds, double voxelSize)
        {
            var voxelGrid = new Dictionary<(int, int, int), List<Vector3>>();

            foreach (var point in points)
            {
                var key = (
                    (int)((point.X - bounds.minX) / voxelSize),
                    (int)((point.Y - bounds.minY) / voxelSize),
                    (int)((point.Z - bounds.minZ) / voxelSize)
                );

                if (!voxelGrid.ContainsKey(key))
                    voxelGrid[key] = new List<Vector3>();

                voxelGrid[key].Add(point);
            }

            return voxelGrid;
        }

        /// <summary>
        /// 提取Vector3代表点
        /// </summary>
        private List<Vector3> ExtractRepresentativePointsVector3(Dictionary<(int, int, int), List<Vector3>> voxelGrid)
        {
            return voxelGrid.Values.Select(voxel => voxel.First()).ToList();
        }

        #endregion

        #region === 2D视图更新 ===

        /// <summary>
        /// 更新切片视图
        /// </summary>
        private void UpdateSliceView()
        {
            if (slices2D.Count == 0 || currentSlice < 0 || currentSlice >= slices2D.Count)
                return;

            slicePlotModel.Series.Clear();
            var slice = slices2D[currentSlice];

            if (slice.Count > 0)
            {
                CreateSliceScatterSeries(slice);
                slicePlotModel.Title = $"切片 {currentSlice + 1}/{slices2D.Count} ({axisLabel} = {sliceLevels[currentSlice]:F3})";

                // 更新轴标签
                UpdateAxisLabels();

                EnforceAspectRatio();
                UpdateSliceInfo(slice);
            }
            else
            {
                // 直接内联：处理空切片
                txtSliceInfo.Text = $"切片 {currentSlice + 1}/{sliceLevels.Count} | 无数据";
            }

            PlotView.InvalidatePlot(true);

            // 直接内联：更新切片列表选择
            if (SectionListBox.Items.Count > currentSlice)
            {
                SectionListBox.SelectedIndex = currentSlice;
            }
        }

        /// <summary>
        /// 创建切片散点图系列
        /// </summary>
        private void CreateSliceScatterSeries(List<System.Windows.Point> slice)
        {
            var scatterSeries = new ScatterSeries
            {
                MarkerType = MarkerType.Circle,
                MarkerSize = CalculateMarkerSize(slice.Count),
                MarkerFill = OxyColors.Blue
            };

            foreach (var point in slice)
            {
                scatterSeries.Points.Add(new ScatterPoint(point.X, point.Y));
            }

            slicePlotModel.Series.Add(scatterSeries);
        }

        /// <summary>
        /// 计算标记大小
        /// </summary>
        private double CalculateMarkerSize(int pointCount)
        {
            return pointCount > 1000 ? 1 : pointCount > 100 ? 2 : 4;
        }

        /// <summary>
        /// 更新轴标签 - 支持多轴向
        /// </summary>
        private void UpdateAxisLabels()
        {
            if (slicePlotModel.Axes.Count >= 2)
            {
                slicePlotModel.Axes[0].Title = "X"; // X轴始终是X
                slicePlotModel.Axes[1].Title = detectedAxis == SliceAxis.Z ? "Y" : "Z"; // Y轴根据切片轴向变化
            }
        }

        /// <summary>
        /// 更新切片信息 - 支持多轴向
        /// </summary>
        private void UpdateSliceInfo(List<System.Windows.Point> slice)
        {
            double level = sliceLevels[currentSlice];
            txtSliceInfo.Text = $"第 {currentSlice + 1}/{sliceLevels.Count} | {axisLabel}={level:F2} | 点数: {slice.Count} | 阈值: {threshold:F2}";
        }





        /// <summary>
        /// 强制等比例显示
        /// </summary>
        private void EnforceAspectRatio()
        {
            if (slices2D.Count == 0 || currentSlice < 0 || currentSlice >= slices2D.Count)
                return;

            var slice = slices2D[currentSlice];
            if (slice.Count == 0) return;

            var (centerX, centerY, halfRange) = CalculateViewBounds(slice);
            ApplyViewBounds(centerX, centerY, halfRange);
        }

        /// <summary>
        /// 计算视图边界
        /// </summary>
        private (double centerX, double centerY, double halfRange) CalculateViewBounds(List<System.Windows.Point> slice)
        {
            var minX = slice.Min(p => p.X);
            var maxX = slice.Max(p => p.X);
            var minY = slice.Min(p => p.Y);
            var maxY = slice.Max(p => p.Y);

            var centerX = (minX + maxX) / 2;
            var centerY = (minY + maxY) / 2;
            var maxRange = Math.Max(maxX - minX, maxY - minY);
            var halfRange = maxRange / 2 * 1.1; // 10% 边距

            return (centerX, centerY, halfRange);
        }

        /// <summary>
        /// 应用视图边界
        /// </summary>
        private void ApplyViewBounds(double centerX, double centerY, double halfRange)
        {
            var xAxis = slicePlotModel.Axes.FirstOrDefault(a => a.Position == AxisPosition.Bottom);
            var yAxis = slicePlotModel.Axes.FirstOrDefault(a => a.Position == AxisPosition.Left);

            if (xAxis != null)
            {
                xAxis.Minimum = centerX - halfRange;
                xAxis.Maximum = centerX + halfRange;
            }

            if (yAxis != null)
            {
                yAxis.Minimum = centerY - halfRange;
                yAxis.Maximum = centerY + halfRange;
            }
        }

        /// <summary>
        /// 填充切片列表框 - 支持多轴向
        /// </summary>
        private void PopulateSectionListBox()
        {
            SectionListBox.Items.Clear();

            for (int i = 0; i < sliceLevels.Count; i++)
            {
                SectionListBox.Items.Add($"切片 {i + 1}: {axisLabel} = {sliceLevels[i]:F3}");
            }

            if (sliceLevels.Count > 0)
            {
                SectionListBox.SelectedIndex = currentSlice;
            }
        }

        #endregion

        #region === UI控制 ===

        /// <summary>
        /// 上一个切片
        /// </summary>
        private void BtnPrevSlice_Click(object sender, RoutedEventArgs e)
        {
            if (currentSlice > 0)
            {
                NavigateToSlice(currentSlice - 1);
            }
        }

        /// <summary>
        /// 下一个切片
        /// </summary>
        private void BtnNextSlice_Click(object sender, RoutedEventArgs e)
        {
            if (currentSlice < slices2D.Count - 1)
            {
                NavigateToSlice(currentSlice + 1);
            }
        }

        /// <summary>
        /// 导航到指定切片 - 支持多轴向
        /// </summary>
        private void NavigateToSlice(int sliceIndex)
        {
            currentSlice = sliceIndex;
            selectedLayerIndex = currentSlice;
            UpdateSliceView();
            Update3DViewWithSelectedLayer();
        }

        /// <summary>
        /// 降采样级别改变事件处理
        /// </summary>
        private void DownsampleLevelUpDown_ValueChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
        {
            if (e.NewValue is int newLevel && newLevel != downsampleLevel)
            {
                downsampleLevel = newLevel;

                // 如果有数据，重新应用降采样
                if (fullPoints.Count > 0)
                {
                    ApplyDownsamplingAndRefresh();
                }
            }
        }

        /// <summary>
        /// 应用降采样并刷新显示
        /// </summary>
        private async void ApplyDownsamplingAndRefresh()
        {
            if (fullPoints.Count == 0) return;

            try
            {
                txtStatus.Text = "正在应用降采样...";

                await Task.Run(() =>
                {
                    // 应用降采样
                    renderPoints = VoxelGridDownsample(fullPoints, downsampleLevel);

                    Dispatcher.Invoke(() =>
                    {
                        // 更新3D显示
                        visualizer.CreatePointCloudVisualizationVector3(renderPoints);

                        // 更新状态信息
                        var reductionRatio = fullPoints.Count > 0 ? (double)renderPoints.Count / fullPoints.Count : 1.0;
                        txtStatus.Text = $"Points: {renderPoints.Count:N0}/{fullPoints.Count:N0} ({reductionRatio:P1}) | 降采样级别: {downsampleLevel}";
                    });
                });
            }
            catch (Exception ex)
            {
                txtStatus.Text = $"降采样失败: {ex.Message}";
            }
        }

        #endregion
    }

    #region 辅助类定义 - 从Viewer.xaml.cs提取并适配
    /// <summary>
    /// 点云加载结果
    /// </summary>
    public class PointCloudLoadResult
    {
        public bool IsSuccess { get; set; }
        public List<Vector3> Points { get; set; } = new();
        public string FilePath { get; set; } = string.Empty;
        public string DataInfo { get; set; } = string.Empty;
        public long LoadTimeMs { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;

        /// <summary>
        /// 创建成功的加载结果
        /// </summary>
        public static PointCloudLoadResult Success(string filePath, List<Vector3> points, long loadTimeMs, string dataInfo)
        {
            return new PointCloudLoadResult
            {
                IsSuccess = true,
                FilePath = filePath,
                Points = points,
                LoadTimeMs = loadTimeMs,
                DataInfo = dataInfo
            };
        }

        /// <summary>
        /// 创建失败的加载结果
        /// </summary>
        public static PointCloudLoadResult Failure(string filePath, string errorMessage)
        {
            return new PointCloudLoadResult
            {
                IsSuccess = false,
                FilePath = filePath,
                ErrorMessage = errorMessage
            };
        }
    }

    /// <summary>
    /// 点云文件加载器 - 适配Parameters页面需求
    /// </summary>
    public class ParametersPointCloudFileLoader
    {
        #region 常量定义
        private const long MaxFileSizeBytes = 100 * 1024 * 1024; // 100MB
        private const double BytesToMB = 1024.0 * 1024.0;
        private const int MaxRenderPoints = 70000000; // 3D渲染最大点数
        #endregion

        #region 事件定义
        public event EventHandler<string>? LoadProgressChanged;
        public event EventHandler<PointCloudLoadResult>? LoadCompleted;
        #endregion

        /// <summary>
        /// 异步加载点云文件
        /// </summary>
        public async Task<PointCloudLoadResult> LoadPointCloudAsync(string filePath)
        {
            var stopwatch = Stopwatch.StartNew();

            try
            {
                // 验证文件存在
                if (!File.Exists(filePath))
                {
                    return PointCloudLoadResult.Failure(filePath, $"文件未找到: {filePath}");
                }

                // 检查大文件
                var fileInfo = new FileInfo(filePath);
                if (fileInfo.Length > MaxFileSizeBytes)
                {
                    var dialogResult = MessageBox.Show(
                        $"所选文件过大，有：({fileInfo.Length / BytesToMB:F1} MB)。加载需要等待。继续？",
                        "大文件警告",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (dialogResult != MessageBoxResult.Yes)
                    {
                        return PointCloudLoadResult.Failure(filePath, "文件加载由用户取消！");
                    }
                }

                LoadProgressChanged?.Invoke(this, "加载中...");

                // 异步加载数据
                var points = new List<Vector3>();
                await Task.Run(() => LoadPointCloudData(filePath, points));

                stopwatch.Stop();

                if (points.Count == 0)
                {
                    return PointCloudLoadResult.Failure(filePath, "没有解析到有效点云数据。请检查文件格式。");
                }

                var dataInfo = AnalyzeLoadedData(points, filePath);
                var result = PointCloudLoadResult.Success(filePath, points, stopwatch.ElapsedMilliseconds, dataInfo);

                LoadCompleted?.Invoke(this, result);
                return result;
            }
            catch (Exception ex)
            {
                var result = PointCloudLoadResult.Failure(filePath, ex.Message);
                LoadCompleted?.Invoke(this, result);
                return result;
            }
        }

        /// <summary>
        /// 加载点云数据
        /// </summary>
        private void LoadPointCloudData(string filePath, List<Vector3> points)
        {
            int validPoints = 0;
            int totalLines = 0;
            int skippedLines = 0;

            using (var reader = new StreamReader(filePath))
            {
                string? line;
                int lineNumber = 0;

                while ((line = reader.ReadLine()) != null)
                {
                    lineNumber++;
                    totalLines++;
                    line = line.Trim();

                    // 跳过空行和注释行
                    if (string.IsNullOrEmpty(line) || line.StartsWith("#") || line.StartsWith("//"))
                    {
                        skippedLines++;
                        continue;
                    }

                    try
                    {
                        // 按多种分隔符分割：逗号、空格、制表符
                        var parts = line.Split(new char[] { ',', ' ', '\t' }, StringSplitOptions.RemoveEmptyEntries);

                        // 至少需要2列（X, Y），Z是可选的
                        if (parts.Length >= 2)
                        {
                            // 解析X和Y坐标（必需）
                            if (float.TryParse(parts[0], NumberStyles.Float, CultureInfo.InvariantCulture, out float x) &&
                                float.TryParse(parts[1], NumberStyles.Float, CultureInfo.InvariantCulture, out float y))
                            {
                                // 解析Z坐标（可选，默认为0）
                                float z = 0;
                                if (parts.Length >= 3 && float.TryParse(parts[2], NumberStyles.Float, CultureInfo.InvariantCulture, out float zValue))
                                {
                                    z = zValue;
                                }

                                points.Add(new Vector3(x, y, z));
                                validPoints++;
                            }
                            else
                            {
                                skippedLines++;
                            }
                        }
                        else
                        {
                            skippedLines++;
                        }
                    }
                    catch
                    {
                        skippedLines++;
                    }
                }
            }

            Debug.WriteLine($"解析完成: {validPoints} 有效点，共 {totalLines} 行 ({skippedLines} 跳过)");
        }

        /// <summary>
        /// 分析加载的数据
        /// </summary>
        private string AnalyzeLoadedData(List<Vector3> points, string filePath)
        {
            if (points.Count == 0)
                return "No data";

            // 分析Z坐标分布
            var zValues = points.Select(p => p.Z).ToList();
            var uniqueZCount = zValues.Distinct().Count();
            var minZ = zValues.Min();
            var maxZ = zValues.Max();

            // 判断是2D还是3D数据
            bool is2D = uniqueZCount == 1 && Math.Abs(minZ - maxZ) < 1e-6;

            // 分析坐标范围
            var xRange = points.Max(p => p.X) - points.Min(p => p.X);
            var yRange = points.Max(p => p.Y) - points.Min(p => p.Y);
            var zRange = maxZ - minZ;

            string dataType = is2D ? "2D" : "3D";
            string rangeInfo = is2D ?
                $"Range: X={xRange:F2}, Y={yRange:F2}" :
                $"Range: X={xRange:F2}, Y={yRange:F2}, Z={zRange:F2}";

            // 根据扩展名检测文件格式
            string extension = System.IO.Path.GetExtension(filePath).ToLower();
            string formatInfo = extension switch
            {
                ".csv" => ".csv",
                ".txt" => ".txt",
                ".dat" => ".dat",
                _ => "Unknown format"
            };

            return $"{dataType} • {formatInfo} • {rangeInfo}";
        }
    }

    /// <summary>
    /// 点云可视化器 - 适配Parameters页面需求，支持流式渲染
    /// </summary>
    public class ParametersPointCloudVisualizer
    {
        private readonly Viewport3DX viewport;
        private PointGeometryModel3D? currentPointCloud;

        // 流式渲染支持
        private List<Vector3> accumulatedPoints = new List<Vector3>();
        private bool isStreamingMode = false;

        public ParametersPointCloudVisualizer(Viewport3DX viewport)
        {
            this.viewport = viewport;
        }

        /// <summary>
        /// 创建点云可视化 - Vector3版本（性能优化）
        /// </summary>
        public void CreatePointCloudVisualizationVector3(List<Vector3> renderPoints)
        {
            // 重置流式渲染状态
            isStreamingMode = false;
            accumulatedPoints.Clear();

            // 移除现有的点云
            if (currentPointCloud != null)
            {
                viewport.Items.Remove(currentPointCloud);
            }

            if (renderPoints.Count == 0)
                return;

            // 直接使用Vector3，无需转换
            accumulatedPoints.AddRange(renderPoints);

            // 创建并渲染点云
            CreatePointCloudFromVector3(renderPoints);

            // 调整相机以适应点云
            AdjustCameraToFitPointCloudVector3(renderPoints);
        }

        /// <summary>
        /// 创建点云可视化 - 兼容Point3D版本
        /// </summary>
        public void CreatePointCloudVisualization(List<Point3D> renderPoints)
        {
            // 转换为Vector3后调用优化版本
            var vector3Points = renderPoints.Select(p => new Vector3((float)p.X, (float)p.Y, (float)p.Z)).ToList();
            CreatePointCloudVisualizationVector3(vector3Points);
        }

        /// <summary>
        /// 增量添加点到现有可视化 - Vector3版本（性能优化）
        /// </summary>
        public void AddPointsToVisualizationVector3(List<Vector3> newPoints)
        {
            if (newPoints.Count == 0) return;

            isStreamingMode = true;

            // 直接使用Vector3，无需转换
            accumulatedPoints.AddRange(newPoints);

            // 更新现有点云几何体
            if (currentPointCloud?.Geometry is PointGeometry3D geometry)
            {
                geometry.Positions = new Vector3Collection(accumulatedPoints);
            }
        }

        /// <summary>
        /// 增量添加点到现有可视化 - 兼容Point3D版本
        /// </summary>
        public void AddPointsToVisualization(List<Point3D> newPoints)
        {
            if (newPoints.Count == 0) return;

            // 转换为Vector3后调用优化版本
            var vector3Points = newPoints.Select(p => new Vector3((float)p.X, (float)p.Y, (float)p.Z)).ToList();
            AddPointsToVisualizationVector3(vector3Points);

        }

        /// <summary>
        /// 从Vector3列表创建点云几何体
        /// </summary>
        private void CreatePointCloudFromVector3(List<Vector3> vector3Points)
        {
            // 移除现有的点云
            if (currentPointCloud != null)
            {
                viewport.Items.Remove(currentPointCloud);
            }

            if (vector3Points.Count == 0) return;

            // 创建点几何体
            var pointGeometry = new PointGeometry3D();
            pointGeometry.Positions = new Vector3Collection(vector3Points);

            // 创建颜色（基于Z坐标的渐变色）
            var colors = new Color4Collection();
            if (vector3Points.Count > 0)
            {
                var minZ = vector3Points.Min(p => p.Z);
                var maxZ = vector3Points.Max(p => p.Z);
                var zRange = maxZ - minZ;

                for (int i = 0; i < vector3Points.Count; i++)
                {
                    var point = vector3Points[i];
                    var normalizedZ = zRange > 0 ? (point.Z - minZ) / zRange : 0.5f;
                    colors.Add(new Color4(1.0f - normalizedZ, normalizedZ, 0.5f, 1.0f));
                }
            }
            pointGeometry.Colors = colors;

            // 创建点云模型
            currentPointCloud = new PointGeometryModel3D
            {
                Geometry = pointGeometry,
                Color = System.Windows.Media.Colors.Blue,
                Size = new Size(vector3Points.Count > 10000 ? 1.0 : vector3Points.Count > 2000 ? 2.0 : 3.0,
                               vector3Points.Count > 10000 ? 1.0 : vector3Points.Count > 2000 ? 2.0 : 3.0)
            };

            // 添加到视口
            viewport.Items.Add(currentPointCloud);
        }

        /// <summary>
        /// 调整相机以适应点云 - Vector3版本（性能优化）
        /// </summary>
        private void AdjustCameraToFitPointCloudVector3(List<Vector3> renderPoints)
        {
            if (renderPoints.Count == 0)
                return;

            // 计算边界框
            var minX = renderPoints.Min(p => p.X);
            var maxX = renderPoints.Max(p => p.X);
            var minY = renderPoints.Min(p => p.Y);
            var maxY = renderPoints.Max(p => p.Y);
            var minZ = renderPoints.Min(p => p.Z);
            var maxZ = renderPoints.Max(p => p.Z);

            var center = new Vector3(
                (minX + maxX) / 2,
                (minY + maxY) / 2,
                (minZ + maxZ) / 2
            );

            var size = Math.Max(Math.Max(maxX - minX, maxY - minY), maxZ - minZ);
            var distance = size * 2.0;

            // 更新相机位置
            viewport.Camera = new HelixToolkit.Wpf.SharpDX.PerspectiveCamera
            {
                Position = new Point3D(center.X + distance, center.Y + distance, center.Z + distance),
                LookDirection = new Vector3D(-distance, -distance, -distance),
                UpDirection = new Vector3D(0, 1, 0),
                NearPlaneDistance = 0.1,
                FarPlaneDistance = distance * 100
            };
        }



        /// <summary>
        /// 获取当前点云模型（用于选中层显示）
        /// </summary>
        public PointGeometryModel3D? GetCurrentPointCloud()
        {
            return currentPointCloud;
        }

        /// <summary>
        /// 公共方法：调整相机以适应点云 - Vector3版本（性能优化）
        /// </summary>
        public void AdjustCameraToFitPointCloudPublicVector3(List<Vector3> points)
        {
            if (points.Count == 0)
                return;

            // 计算边界框
            var minX = points.Min(p => p.X);
            var maxX = points.Max(p => p.X);
            var minY = points.Min(p => p.Y);
            var maxY = points.Max(p => p.Y);
            var minZ = points.Min(p => p.Z);
            var maxZ = points.Max(p => p.Z);

            var center = new Vector3(
                (minX + maxX) / 2,
                (minY + maxY) / 2,
                (minZ + maxZ) / 2
            );

            var size = Math.Max(Math.Max(maxX - minX, maxY - minY), maxZ - minZ);
            var distance = size * 2.0;

            // 更新相机位置
            viewport.Camera = new HelixToolkit.Wpf.SharpDX.PerspectiveCamera
            {
                Position = new Point3D(center.X + distance, center.Y + distance, center.Z + distance),
                LookDirection = new Vector3D(-distance, -distance, -distance),
                UpDirection = new Vector3D(0, 1, 0),
                NearPlaneDistance = 0.1,
                FarPlaneDistance = distance * 100
            };
        }


    }
    #endregion
}