using System.Numerics;
using System.Globalization;

namespace ConsoleApp1
{
    /// <summary>
    /// 点云切片处理类，提供沿不同轴向对点云数据进行切片的功能
    /// </summary>
    public class PointCloudSlicer
    {
        /// <summary>
        /// 沿Y轴对点云数据进行切片操作，生成平行于XOZ平面的层
        /// </summary>
        /// <param name="filePath">点云数据文件路径</param>
        /// <param name="n">期望的层数，必须大于0</param>
        /// <returns>包含所有点的Vector3列表，按层组织</returns>
        /// <exception cref="ArgumentException">当n小于等于0时抛出</exception>
        /// <exception cref="FileNotFoundException">当文件不存在时抛出</exception>
        /// <exception cref="InvalidDataException">当数据格式无效时抛出</exception>
        public List<Vector3> SlicePointCloudByYAxis(string filePath, int n)
        {
            // 参数验证
            if (n <= 0)
            {
                throw new ArgumentException("层数必须大于0", nameof(n));
            }

            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"文件不存在: {filePath}");
            }

            Console.WriteLine($"开始读取点云数据文件: {filePath}");
            
            // 读取所有点云数据
            var points = ReadPointCloudData(filePath);
            
            if (points.Count == 0)
            {
                Console.WriteLine("警告: 文件中没有有效的点云数据");
                return new List<Vector3>();
            }

            Console.WriteLine($"成功读取 {points.Count} 个点");

            // 执行Y轴切片
            return PerformYAxisSlicing(points, n);
        }

        /// <summary>
        /// 沿Y轴对点云数据进行切片操作，生成平行于XOZ平面的层（静默版本，不输出控制台信息）
        /// </summary>
        /// <param name="filePath">点云数据文件路径</param>
        /// <param name="n">期望的层数，必须大于0</param>
        /// <returns>包含所有点的Vector3列表，按层组织</returns>
        public List<Vector3> SlicePointCloudByYAxisSilent(string filePath, int n)
        {
            if (n <= 0)
            {
                throw new ArgumentException("层数必须大于0", nameof(n));
            }

            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"文件不存在: {filePath}");
            }

            var points = ReadPointCloudDataSilent(filePath);
            return PerformYAxisSlicing(points, n, silent: true);
        }

        /// <summary>
        /// 获取点云数据的Y轴切片信息
        /// </summary>
        /// <param name="filePath">点云数据文件路径</param>
        /// <param name="n">期望的层数</param>
        /// <returns>切片信息对象</returns>
        public SlicingInfo GetYAxisSlicingInfo(string filePath, int n)
        {
            if (n <= 0)
            {
                throw new ArgumentException("层数必须大于0", nameof(n));
            }

            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"文件不存在: {filePath}");
            }

            var points = ReadPointCloudDataSilent(filePath);
            
            if (points.Count == 0)
            {
                return new SlicingInfo
                {
                    TotalPoints = 0,
                    MinY = 0,
                    MaxY = 0,
                    LayerHeight = 0,
                    LayerCounts = new int[0]
                };
            }

            float minY = points.Min(p => p.Y);
            float maxY = points.Max(p => p.Y);
            float layerHeight = Math.Abs(maxY - minY) < float.Epsilon ? 0 : (maxY - minY) / n;
            
            var layerCounts = new int[n];
            
            if (layerHeight > 0)
            {
                foreach (var point in points)
                {
                    int layerIndex = (int)Math.Floor((point.Y - minY) / layerHeight);
                    if (layerIndex >= n) layerIndex = n - 1;
                    layerCounts[layerIndex]++;
                }
            }
            else
            {
                layerCounts[0] = points.Count;
            }

            return new SlicingInfo
            {
                TotalPoints = points.Count,
                MinY = minY,
                MaxY = maxY,
                LayerHeight = layerHeight,
                LayerCounts = layerCounts
            };
        }

        /// <summary>
        /// 执行Y轴切片操作的核心逻辑
        /// </summary>
        /// <param name="points">点云数据</param>
        /// <param name="n">层数</param>
        /// <param name="silent">是否静默模式</param>
        /// <returns>切片后的点列表</returns>
        private List<Vector3> PerformYAxisSlicing(List<Vector3> points, int n, bool silent = false)
        {
            // 计算Y轴范围
            float minY = points.Min(p => p.Y);
            float maxY = points.Max(p => p.Y);
            
            if (!silent)
            {
                Console.WriteLine($"Y轴范围: {minY:F6} 到 {maxY:F6}");
            }

            // 如果Y轴范围为0，所有点都在同一层
            if (Math.Abs(maxY - minY) < float.Epsilon)
            {
                if (!silent)
                {
                    Console.WriteLine("所有点都在同一Y坐标上，返回所有点");
                }
                return points;
            }

            // 计算层间距
            float layerHeight = (maxY - minY) / n;
            if (!silent)
            {
                Console.WriteLine($"每层高度: {layerHeight:F6}");
            }

            // 对点进行分层
            var result = new List<Vector3>();
            var layerCounts = new int[n];

            foreach (var point in points)
            {
                // 计算点属于哪一层
                int layerIndex = (int)Math.Floor((point.Y - minY) / layerHeight);
                
                // 处理边界情况：最大Y值的点应该属于最后一层
                if (layerIndex >= n)
                {
                    layerIndex = n - 1;
                }

                layerCounts[layerIndex]++;
                result.Add(point);
            }

            // 输出每层的点数统计
            if (!silent)
            {
                Console.WriteLine("各层点数统计:");
                for (int i = 0; i < n; i++)
                {
                    float layerMinY = minY + i * layerHeight;
                    float layerMaxY = minY + (i + 1) * layerHeight;
                    Console.WriteLine($"第 {i + 1} 层 (Y: {layerMinY:F6} - {layerMaxY:F6}): {layerCounts[i]} 个点");
                }
            }

            return result;
        }

        /// <summary>
        /// 从文件中读取点云数据
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>Vector3点列表</returns>
        private List<Vector3> ReadPointCloudData(string filePath)
        {
            return ReadPointCloudDataInternal(filePath, verbose: true);
        }

        /// <summary>
        /// 从文件中读取点云数据（静默版本）
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>Vector3点列表</returns>
        private List<Vector3> ReadPointCloudDataSilent(string filePath)
        {
            return ReadPointCloudDataInternal(filePath, verbose: false);
        }

        /// <summary>
        /// 从文件中读取点云数据的内部实现
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="verbose">是否输出详细信息</param>
        /// <returns>Vector3点列表</returns>
        private List<Vector3> ReadPointCloudDataInternal(string filePath, bool verbose)
        {
            var points = new List<Vector3>();
            int lineNumber = 0;
            int errorCount = 0;

            try
            {
                using (var reader = new StreamReader(filePath))
                {
                    string? line;
                    while ((line = reader.ReadLine()) != null)
                    {
                        lineNumber++;
                        
                        if (string.IsNullOrWhiteSpace(line))
                        {
                            continue;
                        }

                        try
                        {
                            var parts = line.Split(',');
                            
                            if (parts.Length < 3)
                            {
                                errorCount++;
                                if (verbose && errorCount <= 5)
                                {
                                    Console.WriteLine($"警告: 第 {lineNumber} 行数据格式不正确，列数不足: {line}");
                                }
                                continue;
                            }

                            // 解析x, y, z坐标（前三列）
                            if (float.TryParse(parts[0].Trim(), NumberStyles.Float, CultureInfo.InvariantCulture, out float x) &&
                                float.TryParse(parts[1].Trim(), NumberStyles.Float, CultureInfo.InvariantCulture, out float y) &&
                                float.TryParse(parts[2].Trim(), NumberStyles.Float, CultureInfo.InvariantCulture, out float z))
                            {
                                points.Add(new Vector3(x, y, z));
                            }
                            else
                            {
                                errorCount++;
                                if (verbose && errorCount <= 5)
                                {
                                    Console.WriteLine($"警告: 第 {lineNumber} 行坐标解析失败: {line}");
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            errorCount++;
                            if (verbose && errorCount <= 5)
                            {
                                Console.WriteLine($"警告: 第 {lineNumber} 行处理异常: {ex.Message}");
                            }
                        }
                    }
                }

                if (verbose && errorCount > 5)
                {
                    Console.WriteLine($"总共有 {errorCount} 行数据存在问题（只显示了前5个错误）");
                }
            }
            catch (Exception ex)
            {
                throw new InvalidDataException($"读取文件时发生错误: {ex.Message}", ex);
            }

            return points;
        }
    }

    /// <summary>
    /// 切片信息类，包含切片操作的统计信息
    /// </summary>
    public class SlicingInfo
    {
        /// <summary>
        /// 总点数
        /// </summary>
        public int TotalPoints { get; set; }

        /// <summary>
        /// Y轴最小值
        /// </summary>
        public float MinY { get; set; }

        /// <summary>
        /// Y轴最大值
        /// </summary>
        public float MaxY { get; set; }

        /// <summary>
        /// 每层高度
        /// </summary>
        public float LayerHeight { get; set; }

        /// <summary>
        /// 各层点数统计
        /// </summary>
        public int[] LayerCounts { get; set; } = Array.Empty<int>();

        /// <summary>
        /// 获取格式化的统计信息字符串
        /// </summary>
        /// <returns>统计信息字符串</returns>
        public override string ToString()
        {
            var result = new System.Text.StringBuilder();
            result.AppendLine($"总点数: {TotalPoints}");
            result.AppendLine($"Y轴范围: {MinY:F6} 到 {MaxY:F6}");
            result.AppendLine($"每层高度: {LayerHeight:F6}");
            result.AppendLine("各层点数统计:");
            
            for (int i = 0; i < LayerCounts.Length; i++)
            {
                float layerMinY = MinY + i * LayerHeight;
                float layerMaxY = MinY + (i + 1) * LayerHeight;
                result.AppendLine($"第 {i + 1} 层 (Y: {layerMinY:F6} - {layerMaxY:F6}): {LayerCounts[i]} 个点");
            }
            
            return result.ToString();
        }
    }
}
